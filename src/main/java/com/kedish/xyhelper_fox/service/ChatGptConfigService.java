package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.repo.mapper.ChatGptConfigMapper;
import com.kedish.xyhelper_fox.repo.model.ChatGptConfig;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatGptConfigService {
    @Resource
    private ChatGptConfigMapper chatGptConfigMapper;

    @Resource
    private ChatGptSessionService chatGptSessionService;
    @Resource
    private LocalCache localCache;


    public Map<String, String> getConfigMap(List<String> keys) {
        List<ChatGptConfig> list = chatGptConfigMapper
                .selectList(new QueryWrapper<ChatGptConfig>()
                        .in("`key`", keys));
        return
                list.stream().collect(Collectors.toMap(ChatGptConfig::getKey, ChatGptConfig::getValue));
    }

    public void addOrUpdate(Map<String, String> configMaps) {
        List<ChatGptConfig> list = chatGptConfigMapper
                .selectList(new QueryWrapper<ChatGptConfig>()
                        .in("`key`", configMaps.keySet()));
        List<String> changedKeys = new ArrayList<>();
        list.forEach(chatGptConfig -> {
            if (Objects.equals(chatGptConfig.getValue(), configMaps.get(chatGptConfig.getKey()))) {
                configMaps.remove(chatGptConfig.getKey());
                return;
            }
            chatGptConfig.setValue(configMaps.get(chatGptConfig.getKey()));
            chatGptConfigMapper.updateById(chatGptConfig);
            configMaps.remove(chatGptConfig.getKey());
            changedKeys.add(chatGptConfig.getKey());
        });
        if (!configMaps.isEmpty()) {
            changedKeys.addAll(configMaps.keySet());
            chatGptConfigMapper.insert(configMaps.entrySet().stream().map(entry -> {
                ChatGptConfig chatGptConfig = new ChatGptConfig();
                chatGptConfig.setKey(entry.getKey());
                chatGptConfig.setValue(entry.getValue());
                chatGptConfig.setCreateTime(LocalDateTime.now());
                chatGptConfig.setUpdateTime(LocalDateTime.now());
                return chatGptConfig;
            }).collect(Collectors.toList()));
        }
        // 重新加载配置
        localCache.loadConfig();
        applyConfigChanges(changedKeys);
    }

    private void applyConfigChanges(List<String> changed) {
        if (changed.contains("nodeFreeSize")) {
            chatGptSessionService.applyFreeSizeChange();
        }
        if (changed.contains("virtualPlusSize")) {
            chatGptSessionService.applyVirtualPlusSizeChange();
        }
        if (changed.contains("virtualPlusHeat")) {
            chatGptSessionService.applyVirtualPlusHeatChange();
        }
        if (changed.contains("claudeVirtualSize")) {
            chatGptSessionService.applyClaudeVirtualSizeChange();
        }
        if (changed.contains("grokVirtualSize")) {
            chatGptSessionService.applyGrokVirtualSizeChange();
        }
        if (changed.contains("virtualTeamSize")) {
            chatGptSessionService.applyVirtualTeamSizeChange();
        }
        if (changed.contains("virtualProSize")) {
            chatGptSessionService.applyVirtualProSizeChange();
        }
    }
}
