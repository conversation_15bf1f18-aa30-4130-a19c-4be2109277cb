package com.kedish.xyhelper_fox.utils;

import com.kedish.xyhelper_fox.model.resp.GptCar;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

public class GptCarGenerator {

    private static final char[] CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray();
    private static final int CHARS_LENGTH = CHARS.length;

    public static String generateCarId() {

        return generateRandomCarID();
    }

    // 定义热度枚举
    public enum Heat {
        LOW("低"),
        MEDIUM("中"),
        HIGH("高");

        private final String value;

        Heat(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    // 热度配置类
    public static class HeatConfig {
        private final int lowPercent;    // 低热度占比
        private final int mediumPercent; // 中热度占比
        private final int highPercent;   // 高热度占比

        public HeatConfig(int lowPercent, int mediumPercent, int highPercent) {
            if (lowPercent + mediumPercent + highPercent != 100) {
                throw new IllegalArgumentException("热度分布总和必须为100%");
            }
            this.lowPercent = lowPercent;
            this.mediumPercent = mediumPercent;
            this.highPercent = highPercent;
        }
    }

    // 添加 Count 范围配置类
    public static class CountRange {
        private final int min;
        private final int max;

        public CountRange(int min, int max) {
            if (min < 0 || max < min) {
                throw new IllegalArgumentException("无效的范围设置");
            }
            this.min = min;
            this.max = max;
        }

        public int getRandomCount() {
            return ThreadLocalRandom.current().nextInt(min, max + 1);
        }
    }

    // 获取热度对应的 Count 范围
    private static CountRange getCountRangeByHeat(Heat heat) {
        switch (heat) {
            case LOW:
                return new CountRange(0, 10);
            case MEDIUM:
                return new CountRange(10, 30);
            case HIGH:
                return new CountRange(30, 50);
            default:
                return new CountRange(0, 10);
        }
    }

    private static String generateRandomCarID() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        char[] result = new char[8];
        for (int i = 0; i < 8; i++) {
            result[i] = CHARS[random.nextInt(CHARS_LENGTH)];
        }
        return new String(result);
    }

    // 根据热度获取对应的状态
    private static String getStateByHeat(Heat heat) {
        switch (heat) {
            case LOW:
                return "空闲";
            case MEDIUM:
                return "拥挤";
            case HIGH:
                return "繁忙";
            default:
                return "繁忙";
        }
    }

    // 根据概率获取热度
    private static Heat getRandomHeat(HeatConfig config) {
        int rand = ThreadLocalRandom.current().nextInt(100);
        if (rand < config.lowPercent) {
            return Heat.LOW;
        } else if (rand < config.lowPercent + config.mediumPercent) {
            return Heat.MEDIUM;
        } else {
            return Heat.HIGH;
        }
    }
    // 根据HeatConfig 更改已有车队的热度

    public static void updateHeat(List<GptCar> cars, HeatConfig heatConfig) {
        for (GptCar car : cars) {
            Heat heat = getRandomHeat(heatConfig);
            car.setCount(getCountRangeByHeat(heat).getRandomCount());
            car.setDesc(getStateByHeat(heat));
        }
    }

    // 生成指定数量的GptCar，并按照热度配置分配状态
    // 两参数版本，默认使用 PLUS 标签
    public static List<GptCar> generateGptCars(int n, HeatConfig heatConfig) {
        return generateGptCars(n, heatConfig, "PLUS");
    }

    // 修改后的 generateGptCars 方法，支持自定义标签
    public static List<GptCar> generateGptCars(int n, HeatConfig heatConfig, String carLabel) {
        if (n <= 0) {
            throw new IllegalArgumentException("n必须大于0");
        }

        List<GptCar> cars = new ArrayList<>();
        Set<String> usedIDs = new HashSet<>();

        for (int i = 0; i < n; i++) {
            GptCar car = new GptCar();

            // 生成不重复的carID
            String carID;
            do {
                carID = generateRandomCarID();
            } while (usedIDs.contains(carID));
            usedIDs.add(carID);

            car.setCarID(carID);

            // 根据热度配置随机分配状态
            Heat heat = getRandomHeat(heatConfig);
            // 根据热度设置对应范围的随机 count
            CountRange countRange = getCountRangeByHeat(heat);
            car.setCount(countRange.getRandomCount());

            car.setDesc(getStateByHeat(heat));
            car.setRemark("");
            car.setLabel(carLabel);

            car.setVirtual(true);

            cars.add(car);
        }

        return cars;
    }

    // 修改 main 方法用于测试
    public static void main(String[] args) {
        // 设置热度分布：低热度30%，中热度50%，高热度20%
        HeatConfig config = new HeatConfig(30, 50, 20);

        // 生成10个示例
        List<GptCar> cars = generateGptCars(10, config);

        // 统计各热度数量和平均 count
        Map<String, DoubleSummaryStatistics> stats = cars.stream()
                .collect(Collectors.groupingBy(GptCar::getDesc,
                        Collectors.summarizingDouble(GptCar::getCount)));

        // 打印生成的对象
        for (GptCar car : cars) {
            System.out.println("CarID: " + car.getCarID() +
                    ", Desc: " + car.getDesc() +
                    ", Count: " + car.getCount() +
                    ", Remark: " + car.getRemark());
        }

        // 打印统计结果
        System.out.println("\n分布统计：");
        stats.forEach((k, v) -> System.out.println(k +
                ": 数量=" + v.getCount() +
                ", 平均Count=" + String.format("%.2f", v.getAverage()) +
                ", 最小Count=" + v.getMin() +
                ", 最大Count=" + v.getMax()));
    }
}
