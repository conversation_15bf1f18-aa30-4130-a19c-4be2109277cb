<template>
    <el-tooltip content="车队管理" placement="top">
        <el-alert title="车队管理" type="info" :closable="false"></el-alert>
    </el-tooltip>
    <el-divider>gpt节点配置</el-divider>
    <el-form :model="form.node" label-width="150px" style="max-width: 600px;">
        <el-form-item label="免费节点数量">
            <el-input-number v-model="form.node.nodeFreeSize" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="PLUS虚拟车队数量">
            <el-input-number v-model="form.node.virtualPlusSize" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="TEAM虚拟车队数量">
            <el-input-number v-model="form.node.virtualTeamSize" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="PRO虚拟车队数量">
            <el-input-number v-model="form.node.virtualProSize" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="虚拟车队热度">
            <el-radio-group v-model="form.node.virtualPlusHeat">
                <el-radio label="high">高</el-radio>
                <el-radio label="medium">中</el-radio>
                <el-radio label="low">低</el-radio>
            </el-radio-group>
        </el-form-item>

        <el-form-item label="免费节点名称">
            <el-input v-model="form.node.nodeFreeName" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="4o节点名称">
            <el-input v-model="form.node.node4oName" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="PLUS节点名称">
            <el-input v-model="form.node.nodePlusName" placeholder=""></el-input>
        </el-form-item>
        <el-divider>cluade节点配置</el-divider>
  
        <el-form-item label="开启Claude节点">
            <el-switch v-model="form.node.enableClaude" placeholder="不填不显示cluade节点"></el-switch>
        </el-form-item>
        <el-form-item label="cluade节点名称">
            <el-input v-model="form.node.nodeClaudeName" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="fuClaude访问地址">
            <el-input v-model="form.node.claudeUrl" :min="1" placeholder="不填不显示cluade节点"></el-input>
        </el-form-item>
        
        <!-- Claude地址优先级提示 -->
        <el-alert 
            v-if="form.node.claudeUrl && form.node.lyyClaudeUrl" 
            title="提示：检测到同时配置了两个Claude地址，系统将优先使用fuClaude地址" 
            type="warning" 
            :closable="false"
            style="margin-bottom: 20px;">
        </el-alert>
        
        <el-form-item label="lyyClaude地址">
            <el-input v-model="form.node.lyyClaudeUrl" :min="1" placeholder="不填不显示cluade节点"></el-input>
        </el-form-item>
        <el-form-item label="claude虚拟车队数量">
            <el-input v-model="form.node.claudeVirtualSize" type="number" :min="1" placeholder="不填不显示cluade节点"></el-input>
        </el-form-item>


        <el-divider>grok节点配置</el-divider>
        <el-form-item label="开启grok节点">
            <el-switch v-model="form.node.enableGrok" placeholder="不填不显示cluade节点"></el-switch>
        </el-form-item>
        <el-form-item label="grok节点名称">
            <el-input v-model="form.node.nodeGrokName" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="grok访问地址">
            <el-input v-model="form.node.grokUrl" :min="1" placeholder="不填不显示grok节点"></el-input>
        </el-form-item>
        <el-form-item label="grok虚拟车队数量">
            <el-input v-model="form.node.grokVirtualSize" type="number" :min="1" placeholder="不填不显示grok节点"></el-input>
        </el-form-item>
        <!-- 继续添加其他会员管理相关项 -->
        <el-form-item style="justify-content: flex-end;">
            <el-button type="primary" @click="submitCarSetting">保存</el-button>
        </el-form-item>
    </el-form>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/axios'
const form = reactive({
    node: {
        nodeFreeSize: 0,
        virtualPlusSize: 0,
        virtualTeamSize: 0,
        virtualProSize: 0,
        virtualPlusHeat: 'high',
        nodeFreeName: '',
        node4oName: '',
        nodePlusName: '',
        nodeClaudeName: '',
        nodeGrokName: '',
        enableClaude: false,
        claudeUrl: '',
        lyyClaudeUrl: '',
        enableGrok: false,
        grokUrl: '',
        grokVirtualSize: 0,
        claudeVirtualSize: 0,
    },
})
const submitCarSetting = () => {
    api.post('/api/config/addOrUpdate', form.node).then(res => {
        ElMessage.success('保存成功')
    }).catch(err => {
        ElMessage.error('保存失败')
    })
}
const getNodeConfig = () => {
    api.post('/api/config/get', Object.keys(form.node)).then(res => {
        form.node = res.data.data
        form.node.enableClaude = form.node.enableClaude === 'true'
        form.node.enableGrok = form.node.enableGrok === 'true'

    }).catch(err => {
        ElMessage.error('获取失败')
    })
}
getNodeConfig()
</script>